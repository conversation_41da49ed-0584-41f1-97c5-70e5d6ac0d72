server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: modern-lms-backend
  
  datasource:
    url: ***************************************************************************************************
    username: ${DB_USERNAME:lms_user}
    password: ${DB_PASSWORD:lms_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
  
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:guest}
    password: ${RABBITMQ_PASSWORD:guest}
  
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: 86400000 # 24 hours in milliseconds

# Cloudinary Configuration
cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME:your-cloud-name}
  api-key: ${CLOUDINARY_API_KEY:your-api-key}
  api-secret: ${CLOUDINARY_API_SECRET:your-api-secret}

# Logging
logging:
  level:
    com.lms: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/modern-lms.log

# CORS Configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:5173,http://localhost:3000}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# File Upload Configuration
file:
  upload-dir: ${FILE_UPLOAD_DIR:./uploads}

# Certificate Configuration
app:
  certificate:
    storage-path: ${CERTIFICATE_STORAGE_PATH:./certificates}
    template: certificate-template
    base-url: ${CERTIFICATE_BASE_URL:http://localhost:8080/api/certificates}

# Legacy certificate config (for backward compatibility)
certificate:
  template-path: classpath:templates/certificate-template.html
  output-dir: ${CERTIFICATE_OUTPUT_DIR:./certificates}
