CREATE TABLE courses (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    short_description VARCHAR(500),
    thumbnail_url VARCHAR(500),
    preview_video_url VARCHAR(500),
    price DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    original_price DECIMAL(10, 2),
    category_id VARCHAR(36) NOT NULL,
    level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED') NOT NULL DEFAULT 'BEGINNER',
    duration_minutes INT NOT NULL DEFAULT 0,
    language VARCHAR(50) NOT NULL DEFAULT 'English',
    requirements TEXT,
    what_you_will_learn TEXT,
    instructor_id VARCHAR(36) NOT NULL,
    is_published BOOLEAN NOT NULL DEFAULT FALSE,
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    enrolled_count INT NOT NULL DEFAULT 0,
    average_rating DECIMAL(3, 2) NOT NULL DEFAULT 0.00,
    total_ratings INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_title (title),
    INDEX idx_category (category_id),
    INDEX idx_instructor (instructor_id),
    INDEX idx_level (level),
    INDEX idx_price (price),
    INDEX idx_published (is_published),
    INDEX idx_featured (is_featured),
    INDEX idx_rating (average_rating),
    INDEX idx_created_at (created_at)
);
