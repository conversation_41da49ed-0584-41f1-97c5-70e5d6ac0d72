package com.lms.mapper;

import com.lms.dto.user.UserDto;
import com.lms.entity.User;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-18T03:04:26+0530",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserMapperImpl implements UserMapper {

    @Override
    public UserDto toDto(User user) {
        if ( user == null ) {
            return null;
        }

        UserDto userDto = new UserDto();

        userDto.setApproved( user.isApproved() );
        userDto.setAvatarUrl( user.getAvatarUrl() );
        userDto.setCreatedAt( user.getCreatedAt() );
        userDto.setEmail( user.getEmail() );
        userDto.setEmailVerified( user.isEmailVerified() );
        userDto.setEnabled( user.isEnabled() );
        userDto.setFirstName( user.getFirstName() );
        userDto.setId( user.getId() );
        userDto.setLastName( user.getLastName() );
        userDto.setRole( user.getRole() );
        userDto.setUpdatedAt( user.getUpdatedAt() );

        userDto.setFullName( user.getFullName() );

        return userDto;
    }

    @Override
    public User toEntity(UserDto userDto) {
        if ( userDto == null ) {
            return null;
        }

        User user = new User();

        user.setApproved( userDto.isApproved() );
        user.setAvatarUrl( userDto.getAvatarUrl() );
        user.setCreatedAt( userDto.getCreatedAt() );
        user.setEmail( userDto.getEmail() );
        user.setEmailVerified( userDto.isEmailVerified() );
        user.setEnabled( userDto.isEnabled() );
        user.setFirstName( userDto.getFirstName() );
        user.setId( userDto.getId() );
        user.setLastName( userDto.getLastName() );
        user.setRole( userDto.getRole() );
        user.setUpdatedAt( userDto.getUpdatedAt() );

        return user;
    }
}
