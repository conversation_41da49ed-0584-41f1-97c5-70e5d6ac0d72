import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Search, 
  Filter, 
  Plus,
  Edit3,
  Trash2,
  Ban,
  CheckCircle,
  Mail,
  Calendar,
  MoreVertical,
  User,
  Shield,
  GraduationCap
} from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';

interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN';
  status: 'ACTIVE' | 'SUSPENDED' | 'PENDING';
  avatar?: string;
  joinedAt: string;
  lastActive: string;
  coursesEnrolled?: number;
  coursesCreated?: number;
  totalEarnings?: number;
  location?: string;
  verified: boolean;
}

const UserManagement: React.FC = () => {
  const { user } = useAuthStore();
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'STUDENT' | 'INSTRUCTOR' | 'ADMIN'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'ACTIVE' | 'SUSPENDED' | 'PENDING'>('all');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);

  // Mock data - replace with actual API calls
  const mockUsers: UserData[] = [
    {
      id: '1',
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      role: 'STUDENT',
      status: 'ACTIVE',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      joinedAt: '2024-01-15T10:00:00Z',
      lastActive: '2024-01-25T14:30:00Z',
      coursesEnrolled: 5,
      location: 'New York, USA',
      verified: true
    },
    {
      id: '2',
      firstName: 'Mike',
      lastName: 'Chen',
      email: '<EMAIL>',
      role: 'INSTRUCTOR',
      status: 'ACTIVE',
      joinedAt: '2024-01-10T09:00:00Z',
      lastActive: '2024-01-24T16:45:00Z',
      coursesCreated: 8,
      totalEarnings: 15420.50,
      location: 'San Francisco, USA',
      verified: true
    },
    {
      id: '3',
      firstName: 'Emma',
      lastName: 'Davis',
      email: '<EMAIL>',
      role: 'STUDENT',
      status: 'SUSPENDED',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      joinedAt: '2024-01-12T14:00:00Z',
      lastActive: '2024-01-20T11:20:00Z',
      coursesEnrolled: 3,
      location: 'London, UK',
      verified: false
    },
    {
      id: '4',
      firstName: 'David',
      lastName: 'Wilson',
      email: '<EMAIL>',
      role: 'ADMIN',
      status: 'ACTIVE',
      joinedAt: '2023-12-01T08:00:00Z',
      lastActive: '2024-01-25T09:15:00Z',
      location: 'Toronto, Canada',
      verified: true
    },
    {
      id: '5',
      firstName: 'Lisa',
      lastName: 'Brown',
      email: '<EMAIL>',
      role: 'INSTRUCTOR',
      status: 'PENDING',
      joinedAt: '2024-01-20T12:00:00Z',
      lastActive: '2024-01-22T15:30:00Z',
      coursesCreated: 0,
      totalEarnings: 0,
      location: 'Sydney, Australia',
      verified: false
    }
  ];

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setUsers(mockUsers);
      } catch (error) {
        console.error('Failed to fetch users:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const handleEditUser = (userId: string) => {
    console.log('Edit user:', userId);
    // Open edit user modal/page
  };

  const handleDeleteUser = (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      setUsers(prev => prev.filter(u => u.id !== userId));
    }
  };

  const handleSuspendUser = (userId: string) => {
    setUsers(prev => 
      prev.map(u => 
        u.id === userId 
          ? { ...u, status: u.status === 'SUSPENDED' ? 'ACTIVE' : 'SUSPENDED' as const }
          : u
      )
    );
  };

  const handleVerifyUser = (userId: string) => {
    setUsers(prev => 
      prev.map(u => 
        u.id === userId 
          ? { ...u, verified: !u.verified }
          : u
      )
    );
  };

  const handleSendEmail = (userEmail: string) => {
    window.open(`mailto:${userEmail}`, '_blank');
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return <Shield className="w-4 h-4 text-red-500" />;
      case 'INSTRUCTOR':
        return <GraduationCap className="w-4 h-4 text-blue-500" />;
      default:
        return <User className="w-4 h-4 text-green-500" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'INSTRUCTOR':
        return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20';
      default:
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      case 'SUSPENDED':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const stats = {
    total: users.length,
    students: users.filter(u => u.role === 'STUDENT').length,
    instructors: users.filter(u => u.role === 'INSTRUCTOR').length,
    admins: users.filter(u => u.role === 'ADMIN').length,
    active: users.filter(u => u.status === 'ACTIVE').length,
    suspended: users.filter(u => u.status === 'SUSPENDED').length,
    pending: users.filter(u => u.status === 'PENDING').length
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            User Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage platform users and their permissions
          </p>
        </div>
        <Button variant="primary">
          <Plus className="w-4 h-4 mr-2" />
          Add New User
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
            {stats.total}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Total Users
          </div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {stats.students}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Students
          </div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {stats.instructors}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Instructors
          </div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {stats.admins}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Admins
          </div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {stats.active}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Active
          </div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {stats.suspended}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Suspended
          </div>
        </Card>
        
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
            {stats.pending}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Pending
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">All Roles</option>
            <option value="STUDENT">Students</option>
            <option value="INSTRUCTOR">Instructors</option>
            <option value="ADMIN">Admins</option>
          </select>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="SUSPENDED">Suspended</option>
            <option value="PENDING">Pending</option>
          </select>
        </div>
      </Card>

      {/* Users List */}
      {filteredUsers.length === 0 ? (
        <Card className="p-12 text-center">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No users found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search or filter criteria
          </p>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredUsers.map((userData, index) => (
            <motion.div
              key={userData.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {/* Avatar */}
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center overflow-hidden flex-shrink-0">
                      {userData.avatar ? (
                        <img
                          src={userData.avatar}
                          alt={`${userData.firstName} ${userData.lastName}`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <User className="w-6 h-6 text-white" />
                      )}
                    </div>

                    {/* User Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                          {userData.firstName} {userData.lastName}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleBadgeColor(userData.role)}`}>
                          {userData.role}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(userData.status)}`}>
                          {userData.status}
                        </span>
                        {userData.verified && (
                          <CheckCircle className="w-4 h-4 text-green-500" title="Verified" />
                        )}
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400 mb-2">
                        {userData.email}
                      </p>

                      {/* Stats */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600 dark:text-gray-300">
                            Joined {formatDate(userData.joinedAt)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600 dark:text-gray-300">
                            Last active {formatDate(userData.lastActive)}
                          </span>
                        </div>
                        {userData.coursesEnrolled !== undefined && (
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4 text-gray-400" />
                            <span className="text-gray-600 dark:text-gray-300">
                              {userData.coursesEnrolled} courses enrolled
                            </span>
                          </div>
                        )}
                        {userData.coursesCreated !== undefined && (
                          <div className="flex items-center space-x-2">
                            <GraduationCap className="w-4 h-4 text-gray-400" />
                            <span className="text-gray-600 dark:text-gray-300">
                              {userData.coursesCreated} courses created
                            </span>
                          </div>
                        )}
                        {userData.totalEarnings !== undefined && (
                          <div className="flex items-center space-x-2">
                            <span className="text-green-600 dark:text-green-400 font-medium">
                              ${userData.totalEarnings.toLocaleString()} earned
                            </span>
                          </div>
                        )}
                        {userData.location && (
                          <div className="flex items-center space-x-2">
                            <span className="text-gray-600 dark:text-gray-300">
                              📍 {userData.location}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSendEmail(userData.email)}
                      title="Send Email"
                    >
                      <Mail className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditUser(userData.id)}
                      title="Edit User"
                    >
                      <Edit3 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSuspendUser(userData.id)}
                      title={userData.status === 'SUSPENDED' ? 'Activate User' : 'Suspend User'}
                      className={userData.status === 'SUSPENDED' ? 'text-green-600' : 'text-yellow-600'}
                    >
                      <Ban className="w-4 h-4" />
                    </Button>
                    {userData.id !== user?.id && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteUser(userData.id)}
                        title="Delete User"
                        className="text-red-600"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserManagement;
